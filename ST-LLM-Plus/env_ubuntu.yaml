name: ST-LLM
channels:
  - dglteam
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - blas=1.0=mkl
  - bottleneck=1.3.5=py310ha9d4c09_0
  - brotli-python=1.0.9=py310h6a678d5_8
  - bzip2=1.0.8=h7b6447c_0
  - ca-certificates=2024.3.11=h06a4308_0
  - dgl-cuda11.5=0.9.1post1=py310_0
  - intel-openmp=2023.1.0=hdb19cb5_46306
  - ld_impl_linux-64=2.38=h1181459_1
  - libffi=3.4.4=h6a678d5_0
  - libgcc-ng=11.2.0=h1234567_1
  - libgfortran-ng=11.2.0=h00389a5_1
  - libgfortran5=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - mkl=2023.1.0=h213fc3f_46344
  - mkl-service=2.4.0=py310h5eee18b_1
  - mkl_fft=1.3.8=py310h5eee18b_0
  - mkl_random=1.2.4=py310hdb19cb5_0
  - ncurses=6.4=h6a678d5_0
  - numexpr=2.8.7=py310h85018f9_0
  - numpy-base=1.26.4=py310hb5e798b_0
  - openssl=3.0.14=h5eee18b_0
  - pip=23.3.1=py310h06a4308_0
  - platformdirs=3.10.0=py310h06a4308_0
  - pooch=1.7.0=py310h06a4308_0
  - pysocks=1.7.1=py310h06a4308_0
  - python=3.10.13=h955ad1f_0
  - python-dateutil=2.8.2=pyhd3eb1b0_0
  - python-tzdata=2023.3=pyhd3eb1b0_0
  - pytz=2023.3.post1=py310h06a4308_0
  - readline=8.2=h5eee18b_0
  - six=1.16.0=pyhd3eb1b0_1
  - sqlite=3.41.2=h5eee18b_0
  - tbb=2021.8.0=hdb19cb5_0
  - tk=8.6.12=h1ccaba5_0
  - tzdata=2023c=h04d1e81_0
  - wheel=0.41.2=py310h06a4308_0
  - xz=5.4.5=h5eee18b_0
  - zlib=1.2.13=h5eee18b_0
  - pip:
      - absl-py==2.0.0
      - accelerate==0.31.0
      - aiohttp==3.9.1
      - aiosignal==1.3.1
      - annotated-types==0.7.0
      - async-timeout==4.0.3
      - attrs==23.2.0
      - cachetools==5.3.2
      - certifi==2022.12.7
      - charset-normalizer==2.1.1
      - contourpy==1.2.0
      - cycler==0.12.1
      - dgl==2.1.0
      - easy-torch==1.3.2
      - easydict==1.10
      - einops==0.7.0
      - filelock==3.9.0
      - flash-attention==1.0.0
      - fonttools==4.47.0
      - frozenlist==1.4.1
      - fsspec==2023.12.2
      - google-auth==2.25.2
      - google-auth-oauthlib==1.2.0
      - grpcio==1.60.0
      - h5py==3.11.0
      - huggingface-hub==0.19.4
      - idna==3.4
      - jinja2==3.1.2
      - joblib==1.3.2
      - kiwisolver==1.4.5
      - lightning-utilities==0.10.1
      - markdown==3.5.1
      - markupsafe==2.1.3
      - matplotlib==3.8.2
      - mpmath==1.3.0
      - multidict==6.0.4
      - networkx==3.0
      - numpy==1.22.4
      - oauthlib==3.2.2
      - packaging==23.1
      - pandas==1.3.5
      - peft==0.11.1
      - pillow==9.3.0
      - protobuf==4.23.4
      - psutil==5.9.8
      - pyasn1==0.5.1
      - pyasn1-modules==0.3.0
      - pydantic==2.8.2
      - pydantic-core==2.20.1
      - pyparsing==3.1.1
      - pytorch-lightning==1.9.4
      - pyyaml==6.0.1
      - regex==2023.10.3
      - requests==2.28.1
      - requests-oauthlib==1.3.1
      - rsa==4.9
      - safetensors==0.4.1
      - scikit-learn==1.0.2
      - scipy==1.7.3
      - seaborn==0.13.2
      - sentencepiece==0.2.0
      - setproctitle==1.3.2
      - setuptools==59.5.0
      - sympy==1.10.1
      - tables==3.7.0
      - tensorboard==2.15.1
      - tensorboard-data-server==0.7.2
      - threadpoolctl==3.2.0
      - tokenizers==0.15.0
      - torch==2.1.2+cu121
      - torch-geometric==2.5.3
      - torch-scatter==2.1.1+pt20cu117
      - torch-sparse==0.6.17+pt20cu117
      - torchaudio==2.1.2+cu121
      - torchdata==0.7.1
      - torchmetrics==0.9.3
      - torchvision==0.16.2+cu121
      - tqdm==4.66.1
      - transformers==4.36.2
      - triton==2.1.0
      - typing-extensions==4.12.2
      - urllib3==1.26.13
      - werkzeug==3.0.1
      - yarl==1.9.4
