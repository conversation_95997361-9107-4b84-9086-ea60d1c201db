@article{general_review,
   author = "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>",
   title = "Machine Learning in Earthquake Seismology", 
   journal= "Annual Review of Earth and Planetary Sciences",
   year = "2023",
   volume = "51",
   number = "Volume 51, 2023",
   pages = "105-129",
   doi = "https://doi.org/10.1146/annurev-earth-071822-100323",
   url = "https://www.annualreviews.org/content/journals/10.1146/annurev-earth-071822-100323",
   publisher = "Annual Reviews",
   issn = "1545-4495",
   type = "Journal Article"
  }

@article{cred, 
title={CRED: A Deep Residual Network of Convolutional and Recurrent Units for Earthquake Signal Detection}, 
volume={9}, 
DOI={https://doi.org/10.1038/s41598-019-45748-1}, 
number={1}, 
journal={Scientific Reports}, 
author={<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, Wei<PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON>}, 
year={2019}, 
month={Jul} 
}

@article{convnetquake,
author = {<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON> },
title = {Convolutional neural network for earthquake detection and location},
journal = {Science Advances},
volume = {4},
number = {2},
pages = {e1700578},
year = {2018},
doi = {10.1126/sciadv.1700578},
URL = {https://www.science.org/doi/abs/10.1126/sciadv.1700578},
eprint = {https://www.science.org/doi/pdf/10.1126/sciadv.1700578}
}

@article{gpd,
    author = {Ross, Zachary E. and Meier, Men‐Andrin and Hauksson, Egill and Heaton, Thomas H.},
    title = {Generalized Seismic Phase Detection with Deep Learning},
    journal = {Bulletin of the Seismological Society of America},
    volume = {108},
    number = {5A},
    pages = {2894-2901},
    year = {2018},
    month = {08},
    issn = {0037-1106},
    doi = {10.1785/0120180080},
    url = {https://doi.org/10.1785/0120180080},
    eprint = {https://pubs.geoscienceworld.org/ssa/bssa/article-pdf/108/5A/2894/4345392/bssa-2018080.1.pdf},
}

@article{conv_parameterization, 
title={An Investigation of Rapid Earthquake Characterization Using Single‐Station Waveforms and a Convolutional Neural Network}, 
volume={90}, 
DOI={https://doi.org/10.1785/0220180311}, 
number={2A}, 
journal={Seismological Research Letters}, 
author={Lomax, Anthony and Michelini, Alberto and Jozinović, Dario}, 
year={2019}, 
month={Feb}, 
pages={517–529} 
}

@article{p_and_polarity,
author = {Ross, Zachary E. and Meier, Men-Andrin and Hauksson, Egill},
title = {P Wave Arrival Picking and First-Motion Polarity Determination With Deep Learning},
journal = {Journal of Geophysical Research: Solid Earth},
volume = {123},
number = {6},
pages = {5120-5129},
keywords = {phase picking, deep learning, focal mechanisms, real-time seismology, machine learning},
doi = {https://doi.org/10.1029/2017JB015251},
url = {https://agupubs.onlinelibrary.wiley.com/doi/abs/10.1029/2017JB015251},
eprint = {https://agupubs.onlinelibrary.wiley.com/doi/pdf/10.1029/2017JB015251},
year = {2018}
}

@article{japan_polarity, 
title={P-wave first-motion polarity determination of waveform data in western Japan using deep learning}, 
volume={71}, 
DOI={https://doi.org/10.1186/s40623-019-1111-x}, 
number={1}, 
journal={Earth, Planets and Space}, 
publisher={Springer Nature}, 
author={Hara, Shota and Yukitoshi Fukahata and Yoshihisa Iio}, 
year={2019}, 
month={Nov} 
}

@article{first-step, 
title={The first step is the hardest: pitfalls of representing and tokenizing temporal data for large language models}, 
volume={31}, 
DOI={https://doi.org/10.1093/jamia/ocae090}, 
number={9}, 
journal={Journal of the American Medical Informatics Association}, publisher={Oxford University Press}, 
author={Dimitris Spathis and Fahim Kawsar}, 
year={2024}, 
month={Jul}, 
pages={2151–2158} 
} 

@article{hdmixer, 
title={HDMixer: Hierarchical Dependency with Extendable Patch for Multivariate Time Series Forecasting}, 
volume={38}, 
DOI={https://doi.org/10.1609/aaai.v38i11.29155}, 
number={11}, 
journal={Proceedings of the AAAI Conference on Artificial Intelligence}, 
publisher={Association for the Advancement of Artificial Intelligence}, 
author={Huang, Qihe and Shen, Lei and Zhang, Ruixin and Cheng, Jiahuan and Ding, Shouhong and Zhou, Zhengyang and Wang, Yang}, 
year={2024}, 
month={Mar}, 
pages={12608–12616} 
} 

@article{whick-picker,
author = {Münchmeyer, Jannes and Woollam, Jack and Rietbrock, Andreas and Tilmann, Frederik and Lange, Dietrich and Bornstein, Thomas and Diehl, Tobias and Giunchi, Carlo and Haslinger, Florian and Jozinović, Dario and Michelini, Alberto and Saul, Joachim and Soto, Hugo},
title = {Which Picker Fits My Data? A Quantitative Evaluation of Deep Learning Based Seismic Pickers},
journal = {Journal of Geophysical Research: Solid Earth},
volume = {127},
number = {1},
pages = {e2021JB023499},
doi = {https://doi.org/10.1029/2021JB023499},
url = {https://agupubs.onlinelibrary.wiley.com/doi/abs/10.1029/2021JB023499},
eprint = {https://agupubs.onlinelibrary.wiley.com/doi/pdf/10.1029/2021JB023499},
note = {e2021JB023499 2021JB023499},
year = {2022}
}

@article{dl, 
title={Deep learning}, 
volume={521}, 
ISBN={14764687}, 
url={https://doi.org/10.1038/nature14539}, 
DOI={https://doi.org/10.1038/nature14539}, 
number={7553}, 
journal={Nature}, 
author={LeCun, Yann and Bengio, Yoshua and Hinton, Geoffrey}, 
year={2015}, 
pages={436–444} 
} 

@article{seist, 
title={SeisT: A Foundational Deep-Learning Model for Earthquake Monitoring Tasks}, 
volume={62}, 
DOI={https://doi.org/10.1109/tgrs.2024.3371503}, 
journal={IEEE Transactions on Geoscience and Remote Sensing}, 
publisher={Institute of Electrical and Electronics Engineers}, 
author={Li, Sen and Yang, Xu and Cao, Anye and Wang, Changbin and Liu, Yaoqi and Liu, Yapeng and Niu, Qiang}, 
year={2024}, 
month={Jan}, 
pages={1–15} 
}

@inproceedings{seislm,
title={Seis{LM}: a Foundation Model for Seismic Waveforms},
author={Tianlin Liu and Jannes M{\"u}nchmeyer and Laura Laurenti and Chris Marone and Maarten V. de Hoop and Ivan Dokmani{\'c}},
booktitle={Neurips 2024 Workshop Foundation Models for Science: Progress, Opportunities, and Challenges},
year={2024},
url={https://openreview.net/forum?id=UQD2BM8UE4}
}

@article{magnet, 
title={A Machine‐Learning Approach for Earthquake Magnitude Estimation}, 
volume={47}, 
DOI={https://doi.org/10.1029/2019gl085976}, 
number={1}, 
journal={Geophysical Research Letters}, 
author={Mousavi, S. Mostafa and Beroza, Gregory C.}, 
year={2020}, 
month={Jan} 
} 

@article{eqt, 
title={Earthquake transformer—an attentive deep-learning model for simultaneous earthquake detection and phase picking}, 
volume={11}, 
DOI={https://doi.org/10.1038/s41467-020-17591-w}, 
number={1}, 
journal={Nature Communications}, 
author={Mousavi, S. Mostafa and Ellsworth, William L. and Zhu, Weiqiang and Chuang, Lindsay Y. and Beroza, Gregory C.}, 
year={2020}, 
month={Aug} 
} 

@article{stead, 
title={STanford EArthquake Dataset (STEAD): A Global Data Set of Seismic Signals for AI}, 
volume={7}, 
DOI={https://doi.org/10.1109/access.2019.2947848}, 
journal={IEEE Access}, 
author={Mousavi, S. Mostafa and Sheng, Yixiao and Zhu, Weiqiang and Beroza, Gregory C.}, 
year={2019}, 
pages={179464–179476} 
} 

@article{baznet, 
title={Bayesian-Deep-Learning Estimation of Earthquake Location From Single-Station Observations}, 
volume={58}, 
DOI={https://doi.org/10.1109/tgrs.2020.2988770}, 
number={11}, 
journal={IEEE Transactions on Geoscience and Remote Sensing}, 
publisher={Institute of Electrical and Electronics Engineers}, 
author={S. Mostafa, Mousavi and Beroza, Gregory C}, 
year={2020}, 
month={Nov}, 
pages={8211–8224} 
} 

@article{seisclip, 
title={SeisCLIP: A Seismology Foundation Model Pre-Trained by Multimodal Data for Multipurpose Seismic Feature Extraction}, 
volume={62}, 
DOI={https://doi.org/10.1109/tgrs.2024.3354456}, 
journal={IEEE Transactions on Geoscience and Remote Sensing}, 
publisher={Institute of Electrical and Electronics Engineers}, 
author={Si, Xu and Wu, Xinming and Sheng, Hanlin and Zhu, Jun and Li, Zefeng}, 
year={2024}, 
month={Jan}, 
pages={1–13} 
}

@inproceedings{transformer, 
author = {Vaswani, Ashish and Shazeer, Noam and Parmar, Niki and Uszkoreit, Jakob and Jones, Llion and Gomez, Aidan N and Kaiser, {\L}ukasz and Polosukhin, Illia},
 booktitle = {Proceedings of the Conference and Workshop on Neural Information Processing Systems (NeurIPS)},
 title = {Attention is All you Need},
 url = {https://proceedings.neurips.cc/paper_files/paper/2017/file/3f5ee243547dee91fbd053c1c4a845aa-Paper.pdf},
 volume = {30},
 year = {2017}
}

@inproceedings{voice2series,
  title={Voice2series: Reprogramming acoustic models for time series classification},
  author={Chao-Han Huck Yang and Yun-Yun Tsai and Pin-Yu Chen},
  booktitle={Proceedings of the International Conference on Machine Learning (ICML)},
  year={2021}
}

@article{visionts,
      title={VisionTS: Visual Masked Autoencoders Are Free-Lunch Zero-Shot Time Series Forecasters}, 
      author={Mouxiang Chen and Lefei Shen and Zhuo Li and Xiaoyun Joy Wang and Jianling Sun and Chenghao Liu},
      year={2024},
      eprint={2408.17253},
      journal={arXiv},
      url={https://arxiv.org/abs/2408.17253}, 
}

@inproceedings{llm2code,
author = {Goel, Divyam and Grover, Ramansh and Fard, Fatemeh H.},
title = {On the cross-modal transfer from natural language to code through adapter modules},
year = {2022},
isbn = {9781450392983},
url = {https://doi.org/10.1145/3524610.3527892},
doi = {10.1145/3524610.3527892},
booktitle = {Proceedings of the IEEE/ACM International Conference on Program Comprehension},
pages = {71–81},
numpages = {11},
keywords = {adapters, parameter efficient models, pre-trained language models, transfer learning},
location = {Virtual Event}
}

@inproceedings{llm2protein,
  title = 	 {Reprogramming Pretrained Language Models for Antibody Sequence Infilling},
  author =       {Melnyk, Igor and Chenthamarakshan, Vijil and Chen, Pin-Yu and Das, Payel and Dhurandhar, Amit and Padhi, Inkit and Das, Devleena},
  booktitle = 	 {Proceedings of the International Conference on Machine Learning (ICML)},
  pages = 	 {24398--24419},
  year = 	 {2023},
  volume = 	 {202},
  series = 	 {Proceedings of Machine Learning Research},
  month = 	 {23--29 Jul},
  publisher =    {PMLR},
  pdf = 	 {https://proceedings.mlr.press/v202/melnyk23a/melnyk23a.pdf},
  url = 	 {https://proceedings.mlr.press/v202/melnyk23a.html}
}

@article{dino2geo,
      title={Cross-Domain Foundation Model Adaptation: Pioneering Computer Vision Models for Geophysical Data Analysis}, 
      author={Zhixiang Guo and Xinming Wu and Luming Liang and Hanlin Sheng and Nuo Chen and Zhengfa Bi},
      year={2024},
      eprint={2408.12396},
      journal={arXiv},
      primaryClass={cs.CV},
      url={https://arxiv.org/abs/2408.12396}, 
}

@inproceedings{cross-modal_finetune,
  title = 	 {Cross-Modal Fine-Tuning: Align then Refine},
  author =       {Shen, Junhong and Li, Liam and Dery, Lucio M. and Staten, Corey and Khodak, Mikhail and Neubig, Graham and Talwalkar, Ameet},
  booktitle = 	 {Proceedings of the International Conference on Machine Learning (ICML)},
  pages = 	 {31030--31056},
  year = 	 {2023},
  volume = 	 {202},
  series = 	 {Proceedings of Machine Learning Research},
  month = 	 {23--29 Jul},
  publisher =    {PMLR},
  pdf = 	 {https://proceedings.mlr.press/v202/shen23e/shen23e.pdf},
  url = 	 {https://proceedings.mlr.press/v202/shen23e.html}
}

@article{multimodal, 
title={Multimodal Pathway: Improve Transformers with Irrelevant Data from Other Modalities}, 
volume={35}, 
DOI={https://doi.org/10.1109/cvpr52733.2024.00584}, 
journal={2024 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)}, 
author={Zhang, Yiyuan and Ding, Xiaohan and Gong, Kaixiong and Ge, Yixiao and Shan, Ying and Yue, Xiangyu}, 
year={2024}, 
month={Jun}, 
pages={6108–6117} 
}

@article{diting, 
title={DiTing: A large-scale Chinese seismic benchmark dataset for artificial intelligence in seismology}, 
volume={36}, 
url={https://www.sciencedirect.com/science/article/pii/S1674451922000222}, 
DOI={https://doi.org/10.1016/j.eqs.2022.01.022}, 
number={2}, journal={Earthquake Science}, 
author={Zhao, Ming and Xiao, Zhuowei and Chen, Shi and Fang, Lihua}, year={2023}, 
pages={84–94} 
}

@article{ditingmotion, 
title={DiTingMotion: A deep-learning first-motion-polarity classifier and its application to focal mechanism inversion}, 
volume={11}, 
DOI={https://doi.org/10.3389/feart.2023.1103914}, 
journal={Frontiers in Earth Science}, 
publisher={Frontiers Media}, 
author={Zhao, Ming and Xiao, Zhuowei and Zhang, Miao and Yang, Yun and Tang, Lin and Chen, Shi}, 
year={2023}, 
month={Mar} 
}

@article{phasenet, 
title={PhaseNet: A Deep-Neural-Network-Based Seismic Arrival Time Picking Method}, 
volume={216}, 
DOI={https://doi.org/10.1093/gji/ggy423}, 
number={1}, 
journal={Geophysical Journal International}, 
author={Zhu, Weiqiang and Beroza, Gregory C}, 
year={2018}, 
month={Oct} 
}

@inproceedings{ofa,
  title={{One Fits All}: Power General Time Series Analysis by Pretrained LM},
  author={Tian, Zhou and Peisong, Niu and Xue, Wang and Liang, Sun and Rong, Jin},
  booktitle={NeurIPS},
  year={2023}
}

@inproceedings{test,
title={{TEST}: Text Prototype Aligned Embedding to Activate {LLM}'s Ability for Time Series},
author={Chenxi Sun and Hongyan Li and Yaliang Li and Shenda Hong},
booktitle={The Twelfth International Conference on Learning Representations},
year={2024},
url={https://openreview.net/forum?id=Tuh4nZVb0g}
}

@inproceedings{calf,
title={CALF: Aligning LLMs for Time Series Forecasting via Cross-modal Fine-Tuning}, 
author={Liu, Peiyuan and Guo, Hang and Dai, Tao and Li, Naiqi and Bao, Jigang and Ren, Xudong and Jiang, Yong and Xia, Shu-Tao},
booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
year={2025},
arxiv={2403.07300}
}

@inproceedings{s2ip-llm,
  title={S\textsuperscript{2}IP-LLM: Semantic Space Informed Prompt Learning with LLM for Time Series Forecasting},
  author={Pan, Zijie and Jiang, Yushan and Garg, Sahil and Schneider, Anderson and Nevmyvaka, Yuriy and Song, Dongjin},
  booktitle={Forty-first International Conference on Machine Learning},
  year={2024}
}

@inproceedings{tempo,
title={{TEMPO}: Prompt-based Generative Pre-trained Transformer for Time Series Forecasting},
author={Defu Cao and Furong Jia and Sercan O Arik and Tomas Pfister and Yixiang Zheng and Wen Ye and Yan Liu},
booktitle={The Twelfth International Conference on Learning Representations},
year={2024},
url={https://openreview.net/forum?id=YH5w12OUuU}
}

@inproceedings{time-llm,
  title={{Time-LLM}: Time series forecasting by reprogramming large language models},
  author={Jin, Ming and Wang, Shiyu and Ma, Lintao and Chu, Zhixuan and Zhang, James Y and Shi, Xiaoming and Chen, Pin-Yu and Liang, Yuxuan and Li, Yuan-Fang and Pan, Shirui and Wen, Qingsong},
  booktitle={International Conference on Learning Representations (ICLR)},
  year={2024}
}
@inproceedings{llm-effect,
title={Are Language Models Actually Useful for Time Series Forecasting?},
author={Mingtian Tan and Mike A Merrill and Vinayak Gupta and Tim Althoff and Thomas Hartvigsen},
booktitle={The Thirty-eighth Annual Conference on Neural Information Processing Systems},
year={2024},
url={https://openreview.net/forum?id=DV15UbHCY1}
}

@article{gpt2,
  title={Language Models are Unsupervised Multitask Learners},
  journal={arXiv},
  author={Radford, Alec and Wu, Jeff and Child, Rewon and Luan, David and Amodei, Dario and Sutskever, Ilya},
  year={2019},
  url={https://cdn.openai.com/better-language-models/language_models_are_unsupervised_multitask_learners.pdf}
}

@inproceedings{vit,
title={An Image is Worth 16x16 Words: Transformers for Image Recognition at Scale},
author={Alexey Dosovitskiy and Lucas Beyer and Alexander Kolesnikov and Dirk Weissenborn and Xiaohua Zhai and Thomas Unterthiner and Mostafa Dehghani and Matthias Minderer and Georg Heigold and Sylvain Gelly and Jakob Uszkoreit and Neil Houlsby},
booktitle={International Conference on Learning Representations},
year={2021},
url={https://openreview.net/forum?id=YicbFdNTTy}
}

@inproceedings{patchtst,
  title     = {A Time Series is Worth 64 Words: Long-term Forecasting with Transformers},
  author    = {Nie, Yuqi and
               H. Nguyen, Nam and
               Sinthong, Phanwadee and 
               Kalagnanam, Jayant},
  booktitle = {International Conference on Learning Representations},
  year      = {2023}
}

@inproceedings{lora,
title={Lo{RA}: Low-Rank Adaptation of Large Language Models},
author={Edward J Hu and yelong shen and Phillip Wallis and Zeyuan Allen-Zhu and Yuanzhi Li and Shean Wang and Lu Wang and Weizhu Chen},
booktitle={International Conference on Learning Representations},
year={2022},
url={https://openreview.net/forum?id=nZeVKeeFYf9}
}

@article{stl,
  title={STL: A Seasonal-Trend Decomposition Procedure Based on Loess },
  author={Cleveland, Robert B and Cleveland, William S and McRae, Jean E and Terpenning, Irma and others},
  journal={Journal of Official Statistics},
  volume={6},
  number={1},
  pages={3--73},
  month={Jan},
  year={1990}
}

@inproceedings{swin-transformer,
  title={Swin Transformer: Hierarchical Vision Transformer using Shifted Windows},
  author={Liu, Ze and Lin, Yutong and Cao, Yue and Hu, Han and Wei, Yixuan and Zhang, Zheng and Lin, Stephen and Guo, Baining},
  booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)},
  year={2021}
}

@inproceedings{bert,
    title = "{BERT}: Pre-training of Deep Bidirectional Transformers for Language Understanding",
    author = "Devlin, Jacob  and
      Chang, Ming-Wei  and
      Lee, Kenton  and
      Toutanova, Kristina",
    booktitle = "Proceedings of the 2019 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies",
    month = jun,
    year = "2019",
    url = "https://aclanthology.org/N19-1423",
    doi = "10.18653/v1/N19-1423",
    pages = "4171--4186"
}

@inproceedings{batchnorm,
author = {Ioffe, Sergey and Szegedy, Christian},
title = {Batch normalization: accelerating deep network training by reducing internal covariate shift},
year = {2015},
booktitle = {Proceedings of the 32nd International Conference on Machine Learning},
volume={37},
pages = {448–456},
numpages = {9}
}

@article{gelu,
      title={Gaussian Error Linear Units (GELUs)}, 
      author={Dan Hendrycks and Kevin Gimpel},
      year={2023},
      eprint={1606.08415},
      journal={arXiv},
      primaryClass={cs.LG},
      url={https://arxiv.org/abs/1606.08415}, 
}

@article{clip,
  title={Learning Transferable Visual Models From Natural Language Supervision}, 
  author={Alec Radford and Jong Wook Kim and Chris Hallacy and Aditya Ramesh and Gabriel Goh and Sandhini Agarwal and Girish Sastry and Amanda Askell and Pamela Mishkin and Jack Clark and Gretchen Krueger and Ilya Sutskever},
  journal={arXiv preprint arXiv:2103.00020},
  year={2021}
}

@inproceedings{huggingface,
    title = "Transformers: State-of-the-Art Natural Language Processing",
    author = "Thomas Wolf and Lysandre Debut and Victor Sanh and Julien Chaumond and Clement Delangue and Anthony Moi and Pierric Cistac and Tim Rault and Rémi Louf and Morgan Funtowicz and Joe Davison and Sam Shleifer and Patrick von Platen and Clara Ma and Yacine Jernite and Julien Plu and Canwen Xu and Teven Le Scao and Sylvain Gugger and Mariama Drame and Quentin Lhoest and Alexander M. Rush",
    booktitle = "Proceedings of the 2020 Conference on Empirical Methods in Natural Language Processing: System Demonstrations",
    month = oct,
    year = "2020",
    address = "Online",
    publisher = "Association for Computational Linguistics",
    url = "https://www.aclweb.org/anthology/2020.emnlp-demos.6",
    pages = "38--45"
}

@article{whisper,
      title={Robust Speech Recognition via Large-Scale Weak Supervision}, 
      author={Alec Radford and Jong Wook Kim and Tao Xu and Greg Brockman and Christine McLeavey and Ilya Sutskever},
      year={2022},
      eprint={2212.04356},
      journal={arXiv preprint},
      primaryClass={eess.AS},
      url={https://arxiv.org/abs/2212.04356}, 
}

@article{dall-e,
      title={Zero-Shot Text-to-Image Generation}, 
      author={Aditya Ramesh and Mikhail Pavlov and Gabriel Goh and Scott Gray and Chelsea Voss and Alec Radford and Mark Chen and Ilya Sutskever},
      year={2021},
      eprint={2102.12092},
      journal={arXiv},
      primaryClass={cs.CV},
      url={https://arxiv.org/abs/2102.12092}, 
}

@article{alphafold, 
title={Highly Accurate Protein Structure Prediction with Alphafold}, 
volume={596}, 
url={https://www.nature.com/articles/s41586-021-03819-2}, 
DOI={https://doi.org/10.1038/s41586-021-03819-2}, 
number={7873}, 
journal={Nature}, 
author={Jumper, John and Evans, Richard and Pritzel, Alexander and Green, Tim and Figurnov, Michael and Ronneberger, Olaf and Tunyasuvunakool, Kathryn and Bates, Russ and Žídek, Augustin and Potapenko, Anna and Bridgland, Alex and Meyer, Clemens and Kohl, Simon A. A. and Ballard, Andrew J. and Cowie, Andrew and Romera-Paredes, Bernardino and Nikolov, Stanislav and Jain, Rishub and Adler, Jonas and Back, Trevor}, 
year={2021}, 
month={Jul}, 
pages={583–589} 
} 

@INPROCEEDINGS{sam,
  author={Kirillov, Alexander and Mintun, Eric and Ravi, Nikhila and Mao, Hanzi and Rolland, Chloe and Gustafson, Laura and Xiao, Tete and Whitehead, Spencer and Berg, Alexander C. and Lo, Wan-Yen and Dollár, Piotr and Girshick, Ross},
  booktitle={IEEE/CVF International Conference on Computer Vision (ICCV)}, 
  title={Segment Anything}, 
  year={2023},
  volume={},
  number={},
  pages={3992-4003},
  keywords={Image segmentation;Computer vision;Data privacy;Computational modeling;Data collection;Data models;Task analysis},
  doi={10.1109/ICCV51070.2023.00371},
  url={https://openaccess.thecvf.com/content/ICCV2023/papers/Kirillov_Segment_Anything_ICCV_2023_paper.pdf}
}

@article{llama2,
      title={Llama 2: Open Foundation and Fine-Tuned Chat Models}, 
      author={Hugo Touvron and Louis Martin and Kevin Stone and Peter Albert and Amjad Almahairi and Yasmine Babaei and Nikolay Bashlykov and Soumya Batra and Prajjwal Bhargava and Shruti Bhosale and Dan Bikel and Lukas Blecher and Cristian Canton Ferrer and Moya Chen and Guillem Cucurull and David Esiobu and Jude Fernandes and Jeremy Fu and Wenyin Fu and Brian Fuller and Cynthia Gao and Vedanuj Goswami and Naman Goyal and Anthony Hartshorn and Saghar Hosseini and Rui Hou and Hakan Inan and Marcin Kardas and Viktor Kerkez and Madian Khabsa and Isabel Kloumann and Artem Korenev and Punit Singh Koura and Marie-Anne Lachaux and Thibaut Lavril and Jenya Lee and Diana Liskovich and Yinghai Lu and Yuning Mao and Xavier Martinet and Todor Mihaylov and Pushkar Mishra and Igor Molybog and Yixin Nie and Andrew Poulton and Jeremy Reizenstein and Rashi Rungta and Kalyan Saladi and Alan Schelten and Ruan Silva and Eric Michael Smith and Ranjan Subramanian and Xiaoqing Ellen Tan and Binh Tang and Ross Taylor and Adina Williams and Jian Xiang Kuan and Puxin Xu and Zheng Yan and Iliyan Zarov and Yuchen Zhang and Angela Fan and Melanie Kambadur and Sharan Narang and Aurelien Rodriguez and Robert Stojnic and Sergey Edunov and Thomas Scialom},
      year={2023},
      eprint={2307.09288},
      journal={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2307.09288}, 
}
@inproceedings{adam,
author = {Diederik P. Kingma and Jimmy Ba},
year = {2014},
month = {12},
title = {Adam: A Method for Stochastic Optimization},
booktitle = {International Conference on Learning Representations (ICLR)}
}

@INPROCEEDINGS{cycliclr,
  author={Smith, Leslie N.},
  booktitle={IEEE Winter Conference on Applications of Computer Vision (WACV)}, 
  title={Cyclical Learning Rates for Training Neural Networks}, 
  year={2017},
  volume={},
  number={},
  pages={464-472},
  keywords={Training;Neural networks;Schedules;Computer architecture;Tuning;Computational efficiency},
  doi={10.1109/WACV.2017.58}
}

@inproceedings{convnext,
  author  = {Zhuang Liu and Hanzi Mao and Chao-Yuan Wu and Christoph Feichtenhofer and Trevor Darrell and Saining Xie},
  title   = {A ConvNet for the 2020s},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  year    = {2022},
}